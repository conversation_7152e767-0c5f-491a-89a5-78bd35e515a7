package databaseexport

import "database/sql"

type DatabaseExport struct {
	holo            *sql.DB
	accessKeyId     string
	accessKeySecret string
	endpoint        string
	bucketName      string
	dirName         string
}

func NewDatabaseExport(
	holo *sql.DB,
	accessKeyId string,
	accessKeySecret string,
) *DatabaseExport {
	return &DatabaseExport{
		holo:            holo,
		accessKeyId:     accessKeyId,
		accessKeySecret: accessKeySecret,
		endpoint:        "oss-cn-beijing-internal.aliyuncs.com",
		bucketName:      "maplehaze-crowd",
		dirName:         "oss",
	}
}
