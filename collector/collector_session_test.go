package collector

import (
	"context"
	"errors"
	"log"
	"sync"
	"testing"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	uuid "github.com/satori/go.uuid"
)

func TestCollectorSessionRunWithCancel(t *testing.T) {
	type TestData struct {
		Id    string
		Value int
	}

	collector := NewCollectorSession[TestData]()

	ctx := context.Background()
	ctxTimeout, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	go func() {
		time.Sleep(3 * time.Second)
		cancel()
	}()

	total := 100000

	fetchDataFunc := FetchDataFunc[TestData](
		func(
			fetchContext context.Context,
			fetchOnceLimit int,
			fetchSqlOffset int,
			closeCallbackFunc CloseCallbackFunc,
		) (
			items []TestData,
			fetchSqlLimit int,
			err error,
		) {
			defer func() {
				if err := recover(); err != nil {
					log.Println("FetchDataFunc error:", err)
				}
			}()

			if fetchSqlOffset >= total {
				return
			}

			for i := 0; i < fetchOnceLimit; i++ {
				items = append(items, TestData{Id: uuid.NewV4().String(), Value: i})
			}
			fetchSqlLimit = fetchOnceLimit

			return
		})
	handleDataFunc := HandleDataFunc[TestData](func(handleContext context.Context, items []TestData, closeCallbackFunc CloseCallbackFunc) (err error) {
		defer func() {
			if err := recover(); err != nil {
				log.Println("HandleDataFunc error:", err)
			}
		}()

		log.Println("handleData:", len(items))
		return
	})

	bufferLimitCallbackFunc := BufferLimitCallbackFunc(func(bufferLimitContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		//log.Println("缓冲区已满")
		return
	})

	handleandleDataEmptyCallbackFunc := HandleDataEmptyCallbackFunc(func(handleDataEmptyContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		log.Println("待处理数据为空")
		return
	})

	finishCallbackFunc := FinishCallbackFunc(func(finishContext context.Context) (err error) {
		log.Println("任务结束", finishContext.Err())
		return
	})

	collector.SetFetchDataFunc(&fetchDataFunc)
	collector.SetHandleDataFunc(&handleDataFunc)
	collector.SetBufferLimitCallbackFunc(&bufferLimitCallbackFunc)
	collector.SetHandleDataEmptyCallbackFunc(&handleandleDataEmptyCallbackFunc)
	collector.SetFinishCallbackFunc(&finishCallbackFunc)

	utilities.PrintRuntimeInfoWithTag("init")
	collector.Run(ctxTimeout, 100000, 50000, 20000)
	utilities.PrintRuntimeInfoWithTag("init")

}

func TestCollectorSessionRunWithTimeout(t *testing.T) {
	type TestData struct {
		Id    string
		Value int
	}

	collector := NewCollectorSession[TestData]()

	ctx := context.Background()
	ctxTimeout, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	go func() {
		time.Sleep(30 * time.Second)
		cancel()
	}()

	total := 100000

	fetchDataFunc := FetchDataFunc[TestData](
		func(
			fetchContext context.Context,
			fetchOnceLimit int,
			fetchSqlOffset int,
			closeCallbackFunc CloseCallbackFunc,
		) (
			items []TestData,
			fetchSqlLimit int,
			err error,
		) {
			defer func() {
				if err := recover(); err != nil {
					log.Println("FetchDataFunc error:", err)
				}
			}()

			if fetchSqlOffset >= total {
				return
			}

			for i := 0; i < fetchOnceLimit; i++ {
				items = append(items, TestData{Id: uuid.NewV4().String(), Value: i})
			}
			fetchSqlLimit = fetchOnceLimit

			return
		})
	handleDataFunc := HandleDataFunc[TestData](func(handleContext context.Context, items []TestData, closeCallbackFunc CloseCallbackFunc) (err error) {
		defer func() {
			if err := recover(); err != nil {
				log.Println("HandleDataFunc error:", err)
			}
		}()

		log.Println(len(items))
		return
	})

	bufferLimitCallbackFunc := BufferLimitCallbackFunc(func(bufferLimitContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		//log.Println("缓冲区已满")
		return
	})

	handleandleDataEmptyCallbackFunc := HandleDataEmptyCallbackFunc(func(handleDataEmptyContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		log.Println("待处理数据为空")
		return
	})

	finishCallbackFunc := FinishCallbackFunc(func(finishContext context.Context) (err error) {
		log.Println("任务结束", finishContext.Err())
		return
	})

	collector.SetFetchDataFunc(&fetchDataFunc)
	collector.SetHandleDataFunc(&handleDataFunc)
	collector.SetBufferLimitCallbackFunc(&bufferLimitCallbackFunc)
	collector.SetHandleDataEmptyCallbackFunc(&handleandleDataEmptyCallbackFunc)
	collector.SetFinishCallbackFunc(&finishCallbackFunc)

	utilities.PrintRuntimeInfoWithTag("init")
	collector.Run(ctxTimeout, 100000, 50000, 20000)
	utilities.PrintRuntimeInfoWithTag("init")

}

func TestCollectorSessionWithClose(t *testing.T) {
	type TestData struct {
		Id    string
		Value int
	}

	collector := NewCollectorSession[TestData]()

	ctx := context.Background()
	ctxTimeout, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	go func() {
		time.Sleep(40 * time.Second)
		cancel()
	}()

	handledTotal := 0

	fetchDataFunc := FetchDataFunc[TestData](
		func(
			fetchContext context.Context,
			fetchOnceLimit int,
			fetchSqlOffset int,
			closeCallbackFunc CloseCallbackFunc,
		) (
			items []TestData,
			fetchSqlLimit int,
			err error,
		) {
			defer func() {
				if err := recover(); err != nil {
					log.Println("FetchDataFunc error:", err)
				}
			}()

			for i := 0; i < fetchOnceLimit; i++ {
				items = append(items, TestData{Id: uuid.NewV4().String(), Value: i})
			}
			fetchSqlLimit = fetchOnceLimit

			return
		})

	handleDataFunc := HandleDataFunc[TestData](func(handleContext context.Context, items []TestData, closeCallbackFunc CloseCallbackFunc) (err error) {
		defer func() {
			if err := recover(); err != nil {
				log.Println("HandleDataFunc error:", err)
			}
		}()
		var sesstionMu sync.Mutex
		for i := 0; i < len(items); i++ {
			sesstionMu.Lock()
			if handledTotal >= 100000 {
				closeCallbackFunc(handleContext)
				closeCallbackFunc(handleContext)
				closeCallbackFunc(handleContext)
				closeCallbackFunc(handleContext)

				log.Println("handledTotal is", handledTotal)
				return
			}

			handledTotal = handledTotal + 1
			sesstionMu.Unlock()
		}

		return
	})

	bufferLimitCallbackFunc := BufferLimitCallbackFunc(func(bufferLimitContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		//log.Println("缓冲区已满")
		return
	})

	handleandleDataEmptyCallbackFunc := HandleDataEmptyCallbackFunc(func(handleDataEmptyContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		log.Println("待处理数据为空")
		return
	})

	finishCallbackFunc := FinishCallbackFunc(func(finishContext context.Context) (err error) {
		log.Println("任务结束", finishContext.Err())
		return
	})

	collector.SetFetchDataFunc(&fetchDataFunc)
	collector.SetHandleDataFunc(&handleDataFunc)
	collector.SetBufferLimitCallbackFunc(&bufferLimitCallbackFunc)
	collector.SetHandleDataEmptyCallbackFunc(&handleandleDataEmptyCallbackFunc)
	collector.SetFinishCallbackFunc(&finishCallbackFunc)

	utilities.PrintRuntimeInfoWithTag("init")
	collector.Run(ctxTimeout, 100000, 50000, 20000)
	utilities.PrintRuntimeInfoWithTag("init")

}

func TestCollectorSessionRun(t *testing.T) {
	type TestData struct {
		Id    string
		Value int
	}

	collector := NewCollectorSession[TestData]()

	ctx := context.Background()
	ctxTimeout, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	go func() {
		time.Sleep(40 * time.Second)
		cancel()
	}()

	fetchDataFunc := FetchDataFunc[TestData](
		func(
			fetchContext context.Context,
			fetchOnceLimit int,
			fetchSqlOffset int,
			closeCallbackFunc CloseCallbackFunc,
		) (
			items []TestData,
			fetchSqlLimit int,
			err error,
		) {
			defer func() {
				if err := recover(); err != nil {
					log.Println("FetchDataFunc error:", err)
				}
			}()

			if fetchSqlOffset >= 1000000 {
				return
			}

			for i := 0; i < fetchOnceLimit; i++ {
				items = append(items, TestData{Id: uuid.NewV4().String(), Value: i})
			}
			fetchSqlLimit = fetchOnceLimit

			return
		})

	handleDataFunc := HandleDataFunc[TestData](func(handleContext context.Context, items []TestData, closeCallbackFunc CloseCallbackFunc) (err error) {
		defer func() {
			if err := recover(); err != nil {
				log.Println("HandleDataFunc error:", err)
			}
		}()
		log.Println("单次处理条数: ", len(items))

		return
	})

	bufferLimitCallbackFunc := BufferLimitCallbackFunc(func(bufferLimitContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		//log.Println("缓冲区已满")
		return
	})

	handleandleDataEmptyCallbackFunc := HandleDataEmptyCallbackFunc(func(handleDataEmptyContext context.Context, closeCallbackFunc CloseCallbackFunc) (err error) {
		log.Println("待处理数据为空")
		return
	})

	finishCallbackFunc := FinishCallbackFunc(func(finishContext context.Context) (err error) {
		log.Println("任务结束", finishContext.Err())
		return
	})

	collector.SetFetchDataFunc(&fetchDataFunc)
	collector.SetHandleDataFunc(&handleDataFunc)
	collector.SetBufferLimitCallbackFunc(&bufferLimitCallbackFunc)
	collector.SetHandleDataEmptyCallbackFunc(&handleandleDataEmptyCallbackFunc)
	collector.SetFinishCallbackFunc(&finishCallbackFunc)

	utilities.PrintRuntimeInfoWithTag("init")
	collector.Run(ctxTimeout, 1000000, 50000, 20000)
	utilities.PrintRuntimeInfoWithTag("init")

}

func myFunc(a chan int) {
	go func() {
		//time.Sleep(10 * time.Second)
		a <- 1
	}()
}
func TestRoutine(t *testing.T) {

	a := make(chan int)

	// go func() {
	// 	time.Sleep(10 * time.Second)
	// 	a <- 1
	// }()
	go myFunc(a)
	for {
		select {
		case <-a:
		default:
		}
		utilities.PrintRuntimeInfoWithTag("init")
		time.Sleep(time.Second)
	}
}

func TestPrintRuntimeInfoWithTag(t *testing.T) {

	utilities.PrintRuntimeInfoWithTag("init")

}

func TestChan(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var wg sync.WaitGroup
	c := make(chan int, 3)
	wg.Add(3)

	c <- 0
	utilities.PrintRuntimeInfoWithTag("init")

	go func() {
		for {
			select {
			case <-ctx.Done():
				wg.Done()
				return
			case v, ok := <-c:
				if ok {
					if v > 1000 {
						wg.Done()
						c <- v
						return
					}
					log.Println("Get:", v, "Write: 1")
					c <- v + 1
				}
			}
		}
	}()

	go func() {
		for {
			select {
			case <-ctx.Done():
				wg.Done()
				return
			case v, ok := <-c:
				if ok {
					if v > 1000 {
						wg.Done()
						c <- v
						return
					}
					log.Println("Get:", v, "Write: 2")
					c <- v + 1
				}
			}
		}
	}()

	go func() {
		for {
			select {
			case <-ctx.Done():
				wg.Done()
				return
			case v, ok := <-c:
				if ok {
					if v > 1000 {
						wg.Done()
						c <- v
						return
					}
					log.Println("Get:", v, "Write: 3")
					c <- v + 1
				}
			}
		}
	}()

	wg.Wait()
	utilities.PrintRuntimeInfoWithTag("init")

}

func ErrReturnFuncDeepLevel() (value int, err error) {
	return 1, errors.New("deep level error")
}

func ErrReturnFunc1() (err error) {
	err = errors.New("error1")
	if v, err := ErrReturnFuncDeepLevel(); err == nil {
		log.Println(v)
	} else {
		log.Println(err)
	}

	return
}

func ErrReturnFunc2() (err error) {
	err = errors.New("error2")
	if v, innerErr := ErrReturnFuncDeepLevel(); innerErr == nil {
		log.Println(v)
	} else {
		log.Println(innerErr)

		err = innerErr
	}
	return
}

func TestErrorReturn(t *testing.T) {
	if err := ErrReturnFunc1(); err != nil {
		log.Println("ErrReturnFunc1 return:", err)
	}

	if err := ErrReturnFunc2(); err != nil {
		log.Println("ErrReturnFunc2 return:", err)
	}
}

func TestErrorJoin(t *testing.T) {
	err1 := errors.New("error1")

	err2 := errors.New("error2")

	err := errors.Join(err1, err2)

	log.Println(err)
}
