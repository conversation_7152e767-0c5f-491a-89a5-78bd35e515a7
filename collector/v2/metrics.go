package collector

import (
	"sync/atomic"
	"time"
)

// CollectorMetrics 采集器性能指标
type CollectorMetrics struct {
	// 数据处理计数器
	totalProcessed atomic.Int64
	totalErrors    atomic.Int64
	totalRetries   atomic.Int64

	// 延迟统计
	fetchLatency   atomic.Int64 // 纳秒
	handleLatency  atomic.Int64 // 纳秒
	lastFetchTime  atomic.Int64
	lastHandleTime atomic.Int64

	// 缓冲区统计
	currentBufferSize atomic.Int64
	maxBufferSize     atomic.Int64
	bufferFullCount   atomic.Int64

	// 吞吐量统计
	startTime        time.Time
	lastMetricsReset time.Time
}

// NewCollectorMetrics 创建新的性能指标收集器
func NewCollectorMetrics() *CollectorMetrics {
	now := time.Now()
	return &CollectorMetrics{
		startTime:        now,
		lastMetricsReset: now,
	}
}

// RecordFetch 记录数据获取指标
func (m *CollectorMetrics) RecordFetch(count int, latency time.Duration) {
	m.totalProcessed.Add(int64(count))
	m.fetchLatency.Add(int64(latency))
	m.lastFetchTime.Store(time.Now().UnixNano())
}

// RecordHandle 记录数据处理指标
func (m *CollectorMetrics) RecordHandle(count int, latency time.Duration) {
	m.handleLatency.Add(int64(latency))
	m.lastHandleTime.Store(time.Now().UnixNano())
}

// RecordError 记录错误
func (m *CollectorMetrics) RecordError() {
	m.totalErrors.Add(1)
}

// RecordRetry 记录重试
func (m *CollectorMetrics) RecordRetry() {
	m.totalRetries.Add(1)
}

// UpdateBufferSize 更新缓冲区大小
func (m *CollectorMetrics) UpdateBufferSize(size int64) {
	m.currentBufferSize.Store(size)
	if size > m.maxBufferSize.Load() {
		m.maxBufferSize.Store(size)
	}
}

// RecordBufferFull 记录缓冲区满
func (m *CollectorMetrics) RecordBufferFull() {
	m.bufferFullCount.Add(1)
}

// GetMetrics 获取性能指标
func (m *CollectorMetrics) GetMetrics() map[string]interface{} {
	now := time.Now()
	duration := now.Sub(m.startTime)

	return map[string]interface{}{
		"total_processed":       m.totalProcessed.Load(),
		"total_errors":          m.totalErrors.Load(),
		"total_retries":         m.totalRetries.Load(),
		"avg_fetch_latency_ms":  float64(m.fetchLatency.Load()) / float64(time.Millisecond) / float64(m.totalProcessed.Load()),
		"avg_handle_latency_ms": float64(m.handleLatency.Load()) / float64(time.Millisecond) / float64(m.totalProcessed.Load()),
		"current_buffer_size":   m.currentBufferSize.Load(),
		"max_buffer_size":       m.maxBufferSize.Load(),
		"buffer_full_count":     m.bufferFullCount.Load(),
		"throughput_per_sec":    float64(m.totalProcessed.Load()) / duration.Seconds(),
		"uptime_seconds":        duration.Seconds(),
	}
}

// ResetMetrics 重置性能指标
func (m *CollectorMetrics) ResetMetrics() {
	m.totalProcessed.Store(0)
	m.totalErrors.Store(0)
	m.totalRetries.Store(0)
	m.fetchLatency.Store(0)
	m.handleLatency.Store(0)
	m.currentBufferSize.Store(0)
	m.maxBufferSize.Store(0)
	m.bufferFullCount.Store(0)
	m.lastMetricsReset = time.Now()
}

// GetBufferUsage 获取缓冲区使用率
func (m *CollectorMetrics) GetBufferUsage() float64 {
	maxSize := m.maxBufferSize.Load()
	if maxSize <= 0 {
		return 0
	}
	return float64(m.currentBufferSize.Load()) / float64(maxSize)
}
