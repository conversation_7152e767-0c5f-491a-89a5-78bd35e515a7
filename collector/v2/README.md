# Collector 模块

## 模块概述

Collector 是一个基于 Go 语言实现的高性能异步数据采集器模块，支持泛型，专为大规模数据处理场景设计。

### 核心功能

-   异步数据采集和处理
-   支持泛型的数据结构
-   基于 Channel 的数据缓冲区管理
-   可配置的数据批处理机制

### 适用场景

-   广告系统的数据采集
-   大规模数据异步处理
-   实时数据流处理
-   分批数据处理场景

## 主要功能

### 关键特性

-   支持自定义数据源和处理逻辑
-   内置缓冲区管理机制
-   异步并发处理
-   优雅的任务终止机制
-   完善的回调函数支持

### 核心组件

-   `CollectorSession`: 数据采集会话管理
-   `CollectorSessionBuffer`: 数据缓冲区
-   多种回调函数支持：
    -   数据获取回调
    -   数据处理回调
    -   缓冲区限制回调
    -   空数据处理回调
    -   任务完成回调

## 快速开始

### 安装

```go
go get codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common
```

### 示例代码

```go
type TestData struct {
    Id    string
    Value int
}

// 创建采集器实例
collector := NewCollectorSession[TestData]()

// 设置数据获取函数
fetchDataFunc := FetchDataFunc[TestData](
    func(ctx context.Context, fetchOnceLimit int, fetchSqlOffset int, closeCallbackFunc CloseCallbackFunc) ([]TestData, int, error) {
        // 实现数据获取逻辑
        return items, fetchOnceLimit, nil
    })

// 设置数据处理函数
handleDataFunc := HandleDataFunc[TestData](
    func(ctx context.Context, items []TestData, closeCallbackFunc CloseCallbackFunc) error {
        // 实现数据处理逻辑
        return nil
    })

// 配置采集器
collector.SetFetchDataFunc(&fetchDataFunc)
collector.SetHandleDataFunc(&handleDataFunc)

// 运行采集器
ctx := context.Background()
collector.Run(ctx, bufferLimit, fetchOnceLimit, handleLimit)
```

## 配置说明

### 关键配置项

| 参数           | 说明             | 示例值 |
| -------------- | ---------------- | ------ |
| bufferLimit    | 总数据缓冲区大小 | 100000 |
| fetchOnceLimit | 单次获取数据量   | 50000  |
| handleLimit    | 单次处理数据量   | 20000  |

## API 文档

### BufferManager

动态缓冲区管理器，用于根据系统内存使用情况自动调整数据处理的缓冲区大小。

#### 初始化配置

```go
metrics := NewCollectorMetrics()
bm := NewBufferManager(
    1000,    // 初始缓冲区大小
    100,     // 最小缓冲区大小
    10000,   // 最大缓冲区大小
    metrics, // 性能指标收集器
)
```

#### 动态调整策略

-   自动扩容：当内存使用率低于阈值(42.5%)且未达到最大限制时
-   自动缩容：当内存使用率超过阈值(85%)时
-   调整间隔：默认 5 秒
-   扩容因子：1.5
-   缩容因子：0.5

#### 使用建议

1. 定期调用自动调整函数：

```go
go func() {
    for {
        bm.AdjustBufferSize()
        time.Sleep(time.Second * 5)
    }
}()
```

2. 监控系统负载：

```go
// 检查是否需要缩容
if bm.ShouldShrink() {
    // 执行缩容相关操作
}

// 检查是否可以扩容
if bm.CanGrow() {
    // 执行扩容相关操作
}
```

3. 获取当前缓冲区大小：

```go
size := bm.GetCurrentSize()
```

#### 性能指标

通过 CollectorMetrics 收集以下指标：

-   当前缓冲区大小
-   最大缓冲区大小
-   缓冲区满计数

### CollectorSession

```go
type CollectorSession[T any] struct {}

// 创建新的采集器会话
func NewCollectorSession[T any]() *CollectorSession[T]

// 运行采集器
func (c *CollectorSession[T]) Run(ctx context.Context, bufferLimit int, fetchOnceLimit int, handleLimit int) error
```

### 回调函数

```go
// 数据获取回调
type FetchDataFunc[T any] func(context.Context, int, int, CloseCallbackFunc) ([]T, int, error)

// 数据处理回调
type HandleDataFunc[T any] func(context.Context, []T, CloseCallbackFunc) error

// 缓冲区限制回调
type BufferLimitCallbackFunc func(context.Context, CloseCallbackFunc) error

// 空数据处理回调
type HandleDataEmptyCallbackFunc func(context.Context, CloseCallbackFunc) error

// 完成回调
type FinishCallbackFunc func(context.Context) error
```

## 系统架构

### 流程图

```mermaid
graph TB
    subgraph 初始化阶段
        A[创建CollectorSession] --> B[配置回调函数]
        B --> C[设置运行参数]
        C --> D[初始化缓冲区]
    end

    subgraph 数据收集阶段
        E[FetchData协程] --> F{检查缓冲区}
        F -->|未满| G[获取数据]
        F -->|已满| H[触发BufferLimit回调]
        G --> I[写入缓冲区]
    end

    subgraph 数据处理阶段
        J[HandleData协程] --> K{检查数据}
        K -->|有数据| L[批量处理数据]
        K -->|无数据| M[触发Empty回调]
        L --> N[更新处理状态]
    end

    subgraph 异常处理
        O[错误检测] --> P{错误类型}
        P -->|上下文取消| Q[优雅终止]
        P -->|数据错误| R[错误回调]
        Q --> S[资源清理]
        R --> T[重试机制]
    end

    subgraph 并发控制
        U[WaitGroup] --> V[协程同步]
        W[Context] --> X[生命周期管理]
        Y[Channel] --> Z[数据流控制]
    end

    %% 外部系统交互
    style EX1 stroke-dasharray: 5 5
    style EX2 stroke-dasharray: 5 5
    subgraph 外部系统
        EX1[数据源系统]
        EX2[下游处理系统]
    end

    EX1 -.-> G
    L -.-> EX2

    %% 流程连接
    D --> E
    D --> J
    I --> F
    N --> K
```

## 扩展与贡献

### 添加新的数据源

1. 实现 `FetchDataFunc` 接口
2. 根据数据源特性配置适当的缓冲区大小
3. 实现必要的错误处理和重试机制

### 自定义处理逻辑

1. 实现 `HandleDataFunc` 接口
2. 确保处理逻辑是线程安全的
3. 适当使用 `closeCallbackFunc` 控制处理流程

### 代码规范

-   遵循 Go 标准代码规范
-   确保所有公开接口都有适当的文档注释
-   添加单元测试覆盖主要功能
-   使用 `context` 进行超时和取消控制
