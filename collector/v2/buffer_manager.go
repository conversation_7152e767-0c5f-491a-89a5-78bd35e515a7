package collector

import (
	"runtime"
	"sync/atomic"
	"time"
)

// BufferManager 动态缓冲区管理器，用于自动调整数据处理的缓冲区大小。
//
// 主要功能：
// - 根据系统内存使用情况自动调整缓冲区大小
// - 支持设置最大和最小缓冲区限制
// - 提供可配置的扩容和缩容策略
// - 内置性能指标收集
//
// 使用示例：
//
//	metrics := NewCollectorMetrics()
//	bm := NewBufferManager(
//	    1000,    // 初始缓冲区大小
//	    100,     // 最小缓冲区大小
//	    10000,   // 最大缓冲区大小
//	    metrics, // 性能指标收集器
//	)
//
//	// 定期调用自动调整函数
//	go func() {
//	    for {
//	        bm.AdjustBufferSize()
//	        time.Sleep(time.Second * 5)
//	    }
//	}()
//
//	// 获取当前缓冲区大小
//	size := bm.GetCurrentSize()
type BufferManager struct {
	// 当前缓冲区大小
	currentSize atomic.Int64
	// 最大缓冲区大小
	maxSize atomic.Int64
	// 最小缓冲区大小
	minSize atomic.Int64
	// 扩容因子
	growthFactor float64
	// 缩容因子
	shrinkFactor float64
	// 内存使用阈值（百分比）
	memoryThreshold float64
	// 最后一次调整时间
	lastAdjustTime atomic.Int64
	// 调整间隔（秒）
	adjustInterval int64
	// 指标收集器
	metrics *CollectorMetrics
}

// NewBufferManager 创建新的缓冲区管理器
//
// 参数:
//   - initialSize: 初始缓冲区大小
//   - minSize: 允许的最小缓冲区大小
//   - maxSize: 允许的最大缓冲区大小
//   - metrics: 性能指标收集器，用于记录缓冲区大小变化
//
// 返回:
//   - *BufferManager: 缓冲区管理器实例
//
// 示例:
//
//	bm := NewBufferManager(
//	    1000,    // 初始大小为1000
//	    100,     // 最小不低于100
//	    10000,   // 最大不超过10000
//	    metrics, // 性能指标收集器
//	)
func NewBufferManager(initialSize, minSize, maxSize int64, metrics *CollectorMetrics) *BufferManager {
	bm := &BufferManager{
		growthFactor:    1.5,
		shrinkFactor:    0.5,
		memoryThreshold: 0.85, // 85% 内存使用率阈值
		adjustInterval:  5,    // 5秒调整间隔
		metrics:         metrics,
	}

	bm.currentSize.Store(initialSize)
	bm.maxSize.Store(maxSize)
	bm.minSize.Store(minSize)
	bm.lastAdjustTime.Store(time.Now().Unix())

	return bm
}

// GetCurrentSize 获取当前缓冲区大小
//
// 返回:
//   - int64: 当前缓冲区大小
//
// 示例:
//
//	size := bm.GetCurrentSize()
//	log.Printf("当前缓冲区大小: %d\n", size)
func (bm *BufferManager) GetCurrentSize() int64 {
	return bm.currentSize.Load()
}

// AdjustBufferSize 根据系统负载自动调整缓冲区大小
//
// 调整策略:
//   - 当内存使用率超过阈值(默认85%)时，触发缩容
//   - 当内存使用率低于阈值一半且未达到最大大小时，触发扩容
//   - 调整间隔默认为5秒
//   - 扩容因子为1.5，缩容因子为0.5
//
// 使用建议:
//   - 建议在独立的goroutine中定期调用
//   - 可以配合ShouldShrink和CanGrow方法使用
//
// 示例:
//
//	go func() {
//	    for {
//	        bm.AdjustBufferSize()
//	        time.Sleep(time.Second * 5)
//	    }
//	}()
func (bm *BufferManager) AdjustBufferSize() {
	// 检查调整间隔
	now := time.Now().Unix()
	if now-bm.lastAdjustTime.Load() < bm.adjustInterval {
		return
	}
	bm.lastAdjustTime.Store(now)

	// 获取当前内存使用情况
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	// 计算内存使用率
	memoryUsage := float64(memStats.Alloc) / float64(memStats.Sys)

	currentSize := bm.currentSize.Load()
	maxSize := bm.maxSize.Load()
	minSize := bm.minSize.Load()

	// 记录当前状态
	// log.Printf("[BufferManager] 当前状态 - 内存使用率: %.2f%%, 当前缓冲区大小: %d, 最小: %d, 最大: %d",
	// 	memoryUsage*100, currentSize, minSize, maxSize)

	// 根据内存使用率和系统负载调整缓冲区大小
	if memoryUsage > bm.memoryThreshold {
		// 内存使用率过高，需要缩容
		newSize := int64(float64(currentSize) * bm.shrinkFactor)
		if newSize < minSize {
			newSize = minSize
		}
		bm.currentSize.Store(newSize)
		// log.Printf("[BufferManager] 执行缩容 - 新缓冲区大小: %d (缩容因子: %.2f)", newSize, bm.shrinkFactor)
	} else if memoryUsage < bm.memoryThreshold*0.5 && currentSize < maxSize {
		// 内存充足，可以扩容
		newSize := int64(float64(currentSize) * bm.growthFactor)
		if newSize > maxSize {
			newSize = maxSize
		}
		bm.currentSize.Store(newSize)
		// log.Printf("[BufferManager] 执行扩容 - 新缓冲区大小: %d (扩容因子: %.2f)", newSize, bm.growthFactor)
	} else {
		// log.Printf("[BufferManager] 保持当前大小 - 缓冲区大小: %d", currentSize)
	}

	// 更新指标
	if bm.metrics != nil {
		bm.metrics.UpdateBufferSize(bm.currentSize.Load())
	}
}

// ShouldShrink 检查是否需要缩容
//
// 返回:
//   - bool: 当内存使用率超过阈值时返回true
//
// 使用场景:
//   - 在自定义缓冲区调整逻辑中使用
//   - 用于监控系统是否处于高负载状态
//
// 示例:
//
//	if bm.ShouldShrink() {
//	    // 执行缩容相关操作
//	    log.Println("系统负载过高，建议减少数据处理量")
//	}
func (bm *BufferManager) ShouldShrink() bool {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	return float64(memStats.Alloc)/float64(memStats.Sys) > bm.memoryThreshold
}

// CanGrow 检查是否可以扩容
//
// 返回:
//   - bool: 当内存使用率低且未达到最大限制时返回true
//
// 使用场景:
//   - 在自定义缓冲区调整逻辑中使用
//   - 用于判断是否可以增加数据处理量
//
// 示例:
//
//	if bm.CanGrow() {
//	    // 执行扩容相关操作
//	    log.Println("系统资源充足，可以增加数据处理量")
//	}
func (bm *BufferManager) CanGrow() bool {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	return float64(memStats.Alloc)/float64(memStats.Sys) < bm.memoryThreshold*0.5 &&
		bm.currentSize.Load() < bm.maxSize.Load()
}
