package models

type SupplyAppModel struct {
	// 下游媒体id
	SupplyAppID string `json:"supply_app_id"`
	// 下游媒体name
	SupplyAppName string `json:"supply_app_name"`
	// 下游媒体包名
	BundleID string `json:"bundle_id"`
	// 下游媒体接入类型, 目前 1(sdk), 2(api), 3(sdk+api, omp_ui注释了), 4(rtb), 5(adx), 6(商店)
	IntegrationType int `json:"integration_type"`
	// os, 0: android; 1: ios
	Os int `json:"os"`
	// 是否enable
	IsEnable int `json:"is_enable"`
}
