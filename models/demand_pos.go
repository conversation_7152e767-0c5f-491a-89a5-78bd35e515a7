package models

type DemandPosModel struct {
	// 上游广告位id
	DemandPosID string `json:"demand_pos_id"`
	// 上游广告位name
	DemandPosName string `json:"demand_pos_name"`
	// 上游媒体id
	DemandAppID string `json:"demand_app_id"`
	// 原始上游广告位id
	RawDemandPosID string `json:"raw_demand_pos_id"`
	// 上游广告位出价类型, 0 实时竞价, 1 固定价(实时价优先)
	DemandPriceType int `json:"demand_price_type"`
	// 是否enable
	IsEnable int `json:"is_enable"`
}
