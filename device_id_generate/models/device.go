package models

// DeviceModel ...
type DeviceModel struct {
	// android ios
	Os        string `json:"os,omitempty"`
	OsVersion string `json:"os_version,omitempty"`
	// android
	Imei    string `json:"imei,omitempty"`
	ImeiMd5 string `json:"imei_md5,omitempty"`
	Oaid    string `json:"oaid,omitempty"`
	OaidMd5 string `json:"oaid_md5,omitempty"`
	// ios idfa idfa_md5
	Idfa    string `json:"idfa,omitempty"`
	IdfaMd5 string `json:"idfa_md5,omitempty"`
	// ios 因子字段
	DeviceStartSec     string `json:"device_start_sec,omitempty"`
	Country            string `json:"country,omitempty"`
	Language           string `json:"language,omitempty"`
	DeviceNameMd5      string `json:"device_name_md5,omitempty"`
	HardwareMachine    string `json:"hardware_machine,omitempty"`
	HardwareModel      string `json:"hardware_model,omitempty"`
	PhysicalMemoryByte string `json:"physical_memory_byte,omitempty"`
	HarddiskSizeByte   string `json:"harddisk_size_byte,omitempty"`
	SystemUpdateSec    string `json:"system_update_sec,omitempty"`
	TimeZone           string `json:"time_zone,omitempty"`
	// ios caid_multi
	CAIDMulti []DeviceCAIDMultiModel `json:"caid_multi,omitempty"`
}

type DeviceCAIDMultiModel struct {
	CAID        string `json:"caid"`
	CAIDVersion string `json:"caid_version"`
}
