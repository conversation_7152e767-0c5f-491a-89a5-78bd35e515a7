package rediskeys

// 千万级以下规模采用和holo数据库结构相同的命名方式，即业务名（adx/app）_平台名/应用名（ssp/dsp/chiguaReader）_模块名（device/magic/booklib）_具体描述（clickpoint_lib）
// 千万级以上规模采用简写，原则上至少包括平台名（ssp/dsp）_功能简写（dau/did/ip）或模块+功能简写（mrlib）

// ADX_SSP_DEVICE
const (
	ADX_SSP_DEVICE_DID_MANUFACTURER_WITH_MODELMD5_PREFIX     = "adx_ssp_device_did_manufacturer_with_modelmd5:%s"
	ADX_SSP_DEVICE_DID_DATA_WITH_DIDMD5_PREFIX               = "ssp_did_data_with_didmd5:%s"
	ADX_SSP_DEVICE_DID_TRUSTED_MANUFACTURER_AND_MODEL_PREFIX = "adx_ssp_device_did_trusted_manufacturer_and_model_%s_%s"
)

// ADX_SSP_DEVICE_IP group
const ADX_SSP_DEVICE_IP_PREFIX = "ssp_ip_%s" // ssp_did_[ip]
const (
	ADX_SSP_DEVICE_IP_REQ_NUMBER_FIELDKEY = "req_number_%s_%s" //req_number_[p_app_id]_[dd]

	// TODO: @王春生 待删除
	ADX_SSP_DEVICE_IP_REQ_LAST_TIME_FIELDKEY = "req_last_time_%s_%s" //req_last_time_[p_app_id]_[dd]

	ADX_SSP_DEVICE_IP_EXP_NUMBER_FIELDKEY = "exp_number_%s_%s" //exp_number_[p_app_id]_[dd]
	ADX_SSP_DEVICE_IP_CLK_NUMBER_FIELDKEY = "clk_number_%s_%s" //clk_number_[p_app_id]_[dd]

	ADX_SSP_DEVICE_IP_REQ_NUMBER_SUPPLY_FIELDKEY = "req_number_supply_%s_%s" //req_number_supply_[app_id]_[dd]
	ADX_SSP_DEVICE_IP_EXP_NUMBER_SUPPLY_FIELDKEY = "exp_number_supply_%s_%s" //exp_number_supply_[app_id]_[dd]
	ADX_SSP_DEVICE_IP_CLK_NUMBER_SUPPLY_FIELDKEY = "clk_number_supply_%s_%s" //clk_number_supply_[app_id]_[dd]

)

// ADX_SSP_DEVICE_IP_RDID group
const ADX_SSP_DEVICE_IP_RDID_PREFIX = "ssp_ip_rdid_%s" // ssp_ip_rdid_[ip]
const (
	ADX_SSP_DEVICE_IP_RDID_FIELDKEY     = "did_%s_%s_%s_%s" // did_[p_app_id]_[dd]_[did_md5]_[md5(osv+model)]
	ADX_SSP_DEVICE_IP_RDID_DAU_FIELDKEY = "did_dau_%s_%s"   // did_dau_[p_app_id]_[dd]
)

// ADX_SSP_DEVICE_DID group
const (
	ADX_SSP_DEVICE_DID_PREFIX = "ssp_did_%s" // ssp_did_[did]
)
const (
	ADX_SSP_DEVICE_DID_REQ_NUMBER_FIELDKEY    = "req_number_%s_%s"    //req_number_[p_app_id]_[dd]
	ADX_SSP_DEVICE_DID_REQ_LAST_TIME_FIELDKEY = "req_last_time_%s_%s" //req_last_time_[p_app_id]_[dd]
	ADX_SSP_DEVICE_DID_EXP_NUMBER_FIELDKEY    = "exp_number_%s_%s"    //exp_number_[p_app_id]_[dd]
	ADX_SSP_DEVICE_DID_EXP_LAST_TIME_FIELDKEY = "exp_last_time_%s_%s" //exp_last_time_[p_app_id]_[dd]
	ADX_SSP_DEVICE_DID_CLK_NUMBER_FIELDKEY    = "clk_number_%s_%s"    //clk_number_[p_app_id]_[dd]
	ADX_SSP_DEVICE_DID_CLK_LAST_TIME_FIELDKEY = "clk_last_time_%s_%s" //clk_last_time_[p_app_id]_[dd]

)

// ADX_SSP_DEVICE_DID group, 0-1点随机ttl
const (
	ADX_SSP_STATISTICS_DID_PREFIX = "ssp_statistics_did_%s" // ssp_did_[did]
)
const (
	ADX_SSP_STATISTICS_SUPPLY_DID_REQ_NUMBER_FIELDKEY = "req_number_%s_%s_%s" //req_number_[pos_id]_[p_pos_id]_[dd]
)

// ADX_SSP_MAX_DAU_NUMBER slice: 128片(值0-127)
const (
	// adx_ssp_dau_max_number_ TODO: @王春生
	ADX_SSP_MAX_DAU_NUMBER_KEY = "max_dau_number_%s_%s_%s" // max_dau_number_[p_app_id]_[dd]_[slice]
)

// ADX_SSP_DEVICE_IP_PAPP_ID dau
// const ADX_SSP_DEVICE_IP_PAPP_ID_DAU_KEY = "ssp_ip_papp_dau_%s_%s_%s_%d" // ssp_ip_papp_dau_[data]_[ip]_[p_app_id]_[mod], mod: 0~255
// const ADX_SSP_DEVICE_IP_PAPP_ID_DAU_FIELDKEY = "did_%s"                 // did_[did_md5]

// MH_READER
const (
	MH_READER_BOOKS                       = "mh_reader_books"
	MH_READER_BOOKS_BY_STATUS             = "mh_reader_books_by_status_%d" // status
	MH_READER_BOOKS_WITH_DETAIL           = "mh_reader_books_with_detail"
	MH_READER_BOOKS_WITH_DETAIL_BY_STATUS = "mh_reader_books_with_detail_by_status_%d" // status
	MH_READER_BOOK                        = "mh_reader_book_%s"                        // book_id
	MH_READER_BOOKS_LIST                  = "mh_reader_books_list_%s"                  // list_id
	MH_READER_BOOKS_LIST_ALL              = "mh_reader_books_list_all"
	MH_READER_CHAPTERS                    = "mh_reader_chapters_%s" // book_id
	MH_READER_CHAPTER                     = "mh_chapter_%s"         // chapter_id
)

// /*** MFLIB BEGIN ***/
// const ADX_DMP_FMLIB_KS30DAY_SILENT_DID_KEY = "ssp_mflib_ks30dsdid_%s_%s"             // ssp_mflib_ks30dsdid_[valid_date]_[did]
// const ADX_DMP_FMLIB_KSJS30DAY_SILENT_DID_KEY = "ssp_mflib_ksjs30dsdid_%s_%s"         // ssp_mflib_ksjs30dsdid_[valid_date]_[did]
// const ADX_DMP_FMLIB_JD_DID_KEY = "ssp_mflib_jddid_%s"                                // ssp_mflib_jddid_[did]
// const ADX_DMP_FMLIB_JD_AND_WX_DID_KEY = "ssp_mflib_jdandwxid_%s"                     // ssp_mflib_jdandwxid_[did]
// const ADX_DMP_FMLIB_JD_AND_WX_AND_ALPAY_DID_KEY = "ssp_mflib_jdandwxandalipaydid_%s" // ssp_mflib_jdandwxandalipaydid_[did]
// const ADX_DMP_FMLIB_TB_DID_KEY = "ssp_mflib_tbdid_%s"                                // ssp_mflib_tbdid_[did]
// const ADX_DMP_FMLIB_PDD_DID_KEY = "ssp_mflib_pdddid_%s"                              // ssp_mflib_pdddid_[did]
// const ADX_DMP_FMLIB_ALIPAY_DID_KEY = "ssp_mflib_alipaydid_%s"                        // ssp_mflib_alipaydid_[did]
// const ADX_DMP_FMLIB_KS_PLAN_ID_DID_KEY = "ssp_mflib_ks_planid_%s"
// const ADX_DMP_FMLIB_KSJS_PLAN_ID_DID_KEY = "ssp_mflib_ksjs_planid_%s"

// 人群包新KEY
const ADX_DMP_CROWD_DID_KEY = "dmp_crowd_%s_%s" // dmp_crowd_[crowd_id]_[did]

// const ADX_DMP_FMLIB_KS30DAY_SILENT_DID_TOTAL_KEY = "ssp_mflib_ks30dsdid_total_%s"             // ssp_mflib_ks30dsdid_total_[date]
// const ADX_DMP_FMLIB_KSJS30DAY_SILENT_DID_TOTAL_KEY = "ssp_mflib_ksjs30dsdid_total_%s"         // ssp_mflib_ksjs30dsdid_total_[date]
// const ADX_DMP_FMLIB_JD_DID_TOTAL_KEY = "ssp_mflib_jddid_total"                                // ssp_mflib_jddid_total
// const ADX_DMP_FMLIB_JD_AND_WX_DID_TOTAL_KEY = "ssp_mflib_jdandwxid_total"                     // ssp_mflib_jdandwxid_total
// const ADX_DMP_FMLIB_JD_AND_WX_AND_ALPAY_DID_TOTAL_KEY = "ssp_mflib_jdandwxandalipaydid_total" // ssp_mflib_jdandwxandalipaydid_total
// const ADX_DMP_FMLIB_TB_DID_TOTAL_KEY = "ssp_mflib_tbdid_total"                                // ssp_mflib_tbdid_total
// const ADX_DMP_FMLIB_PDD_DID_TOTAL_KEY = "ssp_mflib_pdddid_total"                              // ssp_mflib_pdddid_total
// const ADX_DMP_FMLIB_ALIPAY_DID_TOTAL_KEY = "ssp_mflib_alipaydid_total"                        // ssp_mflib_alipaydid_total
// const ADX_DMP_FMLIB_KS_PLAN_ID_DID_TOTAL_KEY = "ssp_mflib_ks_planid_total"
// const ADX_DMP_FMLIB_KSJS_PLAN_ID_DID_TOTAL_KEY = "ssp_mflib_ksjs_planid_total"

/*** MFLIB END ***/

const ADX_DEVICE_STATISTICS_IP_DEMAND_BLACKLIST_KEY = "adx_device_statistics_ip_demand_blacklist"

// const ADX_DEVICE_STATISTICS_IP_DEMAND_WHITELIST_KEY = "adx_device_statistics_ip_demand_whitelist"
const ADX_DEVICE_STATISTICS_DAU_SUPPLY_BLACKLIST_KEY = "adx_device_statistics_dau_supply_blacklist"
const ADX_DEVICE_STATISTICS_DAU_DEMAND_BLACKLIST_KEY = "adx_device_statistics_dau_demand_blacklist"
const ADX_DEVICE_STATISTICS_DAU_SUPPLY_WHITELIST_KEY = "adx_device_statistics_dau_supply_whitelist"
const ADX_DEVICE_STATISTICS_DAU_DEMAND_WHITELIST_KEY = "adx_device_statistics_dau_demand_whitelist"
const ADX_DEVICE_MHID_SUPPLY_BLACKLIST_KEY = "adx_device_mhid_supply_blacklist"
const ADX_DEVICE_MHID_SUPPLY_VIPLIST_KEY = "adx_device_mhid_supply_viplist"

// const DSP_MAGIC_DYNAMIC_TARGET_URL_KEY = "dsp_dt_url_%s" // task id

const ADX_SSP_CORE_YOUKU_AUDIT_TOKEN_KEY = "adx_ssp_core_youku_audit_token"

const ADX_SSP_EXTRA_EXP_TOTAL_KEY = "ssp_extra_exp_total_%s" // ssp_extra_exp_total_[mg_deal_id]

const ADX_SSP_DEVICE_UA_DICT_KEY = "adx_ssp_device_ua_dict_%s_%s_%s" // adx_ssp_device_ua_dict_厂商_机型_osv

const SSP_MATERIAL_RESP_MINUTE_COUNT_KEY = "ssp_material_resp_minute_count_%s_%s_%s_%s" // ssp_material_resp_minute_count_[app_id]_天_小时_分钟 上游竞胜数

const SSP_MATERIAL_RESP_INTERCEPT_MINUTE_COUNT_KEY = "ssp_material_resp_intercept_minute_count_%s_%s_%s_%s" // ssp_material_resp_intercept_minute_count_[app_id]_天_小时_分钟 素材拦截数

const SSP_MATERIAL_RESP_REPEAT_CRID_MINUTE_COUNT_KEY = "ssp_material_resp_repeat_crid_minute_count_%s_%s_%s_%s" // ssp_material_resp_repeat_crid_minute_count_[app_id]_天_小时_分钟 下游crid返回数

const MH_PLATFORM_MEMCACHE_REDISKEYS_WHITELIST_KEY = "mh_platform_memcache_rediskeys_whitelist"

const SSP_ANTICHEAT_DEMAND_SINGLE_DID_EXP_WHITELIST_KEY = "ssp_anticheat_demand_single_did_exp_whitelist"
const SSP_ANTICHEAT_DEMAND_SINGLE_DID_EXP_PLATFORM_WHITELIST_KEY = "ssp_anticheat_demand_single_did_exp_platform_whitelist"
const SSP_ANTICHEAT_DEMAND_SINGLE_DID_EXP_PLATFORM_APP_FILTERLIST_KEY = "ssp_anticheat_demand_single_did_exp_platform_app_filterlist"
const SSP_ANTICHEAT_DEMAND_SINGLE_DID_EXP_CORP_WHITELIST_KEY = "ssp_anticheat_demand_single_did_exp_corp_whitelist"
const SSP_ANTICHEAT_DEMAND_SINGLE_DID_EXP_CORP_APP_FILTERLIST_KEY = "ssp_anticheat_demand_single_did_exp_corp_app_filterlist"

const SSP_CRASH_MONITOR_APP_STAT_MIN_KEY = "ssp_crash_monitor_app_stat_%s_%s_%s_%s" // ssp_crash_monitor_app_stat_[bundle_id_md5]_[dd]_[hh]_[mm]  TTL:26h

const ADX_SSP_DEVICE_DPI_KEY = "adx_ssp_device_dpi_%s_%s_%s_%s"                            // adx_ssp_device_dpi_厂商_机型_os_osv
const ADX_SSP_DEVICE_DPI_ABNORMAL_KEY = "adx_ssp_device_dpi_abnormal_%s_%s"                // adx_ssp_device_dpi_abnormal_os_osv
const ADX_SSP_DEVICE_DPI_MINIMUM_GUARANTEE_KEY = "adx_ssp_device_dpi_minimum_guarantee_%s" // adx_ssp_device_dpi_minimum_guarantee_os

const ADX_SSP_VIVO_ADVERTISER_KEY = "adx_ssp_vivo_advertiser_%s" // adx_ssp_vivo_advertiser_[name]

// DID精准替换KEY, ssp_replace_unique_did_[did_md5]_[p_app_id]_[ip]_[osv]_[model]
const ADX_SSP_REPLACE_UNIQUE_DID_KEY = "ssp_replace_unique_did_%s_%s_%s_%s_%s"

// DID精准替换KEY记录ip最大值, ssp_replace_unique_did_[replace_did_md5]_[p_app_id]
const ADX_SSP_REPLACE_UNIQUE_DID_IP_KEY = "ssp_replace_unique_did_ip_%s_%s"

// 快手 crid 替换素材内容
const ADX_SSP_KS_REPLACE_CRID_KEY = "adx_ssp_ks_replace_crid_%s" // adx_ssp_ks_replace_crid_[crid]

// mhid
const ADX_MHID_AUTOINDEX = "adx_mhid_autoindex_%d"                              // adx_mhid_autoindex_[mod]
const ADX_MHID_KEY = "mhid:%s"                                                  // mhid:[did_md5]
const ADX_MHID_SUPPLY_GLOBAL_KEY = "dau_supply_global_%s"                       // dau_supply_global_[dd]
const ADX_MHID_SUPPLY_APP_KEY = "dau_supply_app_%s_%s"                          // dau_supply_app_[dd]_[app_id]
const ADX_MHID_DEMAND_APP_KEY = "dau_demand_app_%s_%s_%s_%s"                    // dau_demand_app_[dd]_[platform_id]_[corp_id]_[app_id]
const ADX_MHID_DEMAND_CORP_KEY = "dau_demand_corp_%s_%s_%s"                     // dau_demand_corp_[dd]_[platform_id]_[corp_id]
const ADX_MHID_DEMAND_PLATFORM_KEY = "dau_demand_platform_%s_%s"                // dau_demand_platform_[dd]_[platform_id]
const ADX_MHID_COLLECTOR_SWITCH_STATUS_KEY = "adx_mhid_collector_switch_status" // mhid 采集器状态位
const ADX_MHID_CLEANER_SWITCH_STATUS_KEY = "adx_mhid_cleaner_switch_status"     // mhid 清理器状态位
const ADX_MHID_RESTORER_SWITCH_STATUS_KEY = "adx_mhid_restorer_switch_status"   // mhid 恢复器状态位

// 统计上游点击率
const STATISTICS_REALTIME_BY_DEMAND_POS_KEY = "statistics_realtime_by_demand_pos_%s_%s" // statistics_realtime_by_demand_pos_[p_app_id]_[p_pos_id]

// 统计下游点击率
const STATISTICS_REALTIME_BY_SUPPLY_DEMAND_POS_KEY = "statistics_realtime_by_supply_demand_pos_%s_%s_%s_%s" // statistics_realtime_by_demand_pos_[app_id]_[pos_id]_[p_app_id]_[p_pos_id]

// sdk appid=10808 applist 临时写入redis
const ADX_SSP_SDK_APPLIST_DIDMD5_KEY = "adx_ssp_sdk_applist_didmd5_%s" // ADX_SSP_sdk_applist_didmd5_[didmd5]

// 快手rta redis key
const RTA_CACHE_DIDMD5_KEY = "rta_cache_%s_%s_%s" // rta_cache_[maplehaze_rta_id]_[dd]_[didmd5]
// sdk 点击反作弊
const ADX_SSP_ANTICHEAT_KEY = "ssp_anticheat_%s"             // ssp_anticheat_[did_md5]
const ADX_SSP_ANTICHEAT_CONFFIG_KEY = "ssp_anticheat_config" // ssp_anticheat_config
// dict query

const ADX_SSP_DICT_QUERY_KEY = "ssp_dict_query"

// debug timout临时redis
const ADX_SSP_DEBUG_TIMEOUT_RANDOM_KEY = "ssp_debug_timeout_random_save_holo_key"
const ADX_SSP_DEBUG_TIMEOUT_MAX_KEY = "ssp_debug_timeout_max_value_key"

// 新的dpi key
const ADX_SSP_NEW_DEVICE_DPI_KEY = "adx_ssp_new_device_dpi" // adx_ssp_new_device_dpi
// 新的ua key
const ADX_SSP_NEW_DEVICE_UA_DICT_KEY = "adx_ssp_new_device_ua_dict" // adx_ssp_new_device_ua_dict
