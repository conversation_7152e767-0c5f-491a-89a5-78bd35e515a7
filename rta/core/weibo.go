package core

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/models"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/utils"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	jsoniter "github.com/json-iterator/go"
)

const (
	weiboRtaUrl         = "https://rta.weibo.com/api/v1/bid" // 微博RTA请求URL
	weiboRtaTimeout     = 200 * time.Millisecond             // 微博RTA请求超时时间
	weiboRspCodeSuccess = "0"                                // 微博RTA成功响应码
	weiboIsBidding      = 1                                  // 微博RTA竞价成功标识 1:成功

)

var (
	// rtaid文档：https://kea58ff4ba.docs.qq.com/sheet/DYlFwUVNNUmhZWWt6eWtGT1VI?tab=000001
	maplehazeRtaIdMap = map[string]string{
		"mh100015": "vb-100015",
	}
)

// WeiboRtaRequest 定义微博RTA请求的数据结构
// 包含请求ID、请求时间、RTA ID列表和设备信息
type WeiboRtaRequest struct {
	ReqID   string             `json:"req_id"`   // 请求唯一标识符
	ReqTime int64              `json:"req_time"` // 请求时间戳，毫秒级
	RtaID   []string           `json:"rta_id"`   // RTA ID列表，用于标识广告主
	Device  WeiboRtaDeviceInfo `json:"device"`   // 用户设备信息
}

// WeiboRtaDeviceInfo 定义微博RTA请求中的设备信息结构
// 包含操作系统和各种设备标识符
type WeiboRtaDeviceInfo struct {
	OS        string                  `json:"os"`                   // 操作系统类型：android或ios
	Imei      string                  `json:"imei,omitempty"`       // Android设备的IMEI原值
	ImeiMd5   string                  `json:"imei_md5"`             // IMEI的MD5值
	Oaid      string                  `json:"oaid,omitempty"`       // Android 10+设备的OAID原值
	OaidMd5   string                  `json:"oaid_md5"`             // OAID的MD5值
	Idfa      string                  `json:"idfa,omitempty"`       // iOS设备的IDFA原值
	IdfaMd5   string                  `json:"idfa_md5"`             // IDFA的MD5值
	CAIDMulti []WeiboRtaCAIDMultiInfo `json:"caid_multi,omitempty"` // iOS设备的CAID多版本信息
}

// WeiboRtaCAIDMultiInfo 定义CAID(中国广告协会ID)的多版本信息结构
type WeiboRtaCAIDMultiInfo struct {
	CAID        string `json:"caid"`         // CAID值
	CAIDVersion string `json:"caid_version"` // CAID算法版本
}

// WeiboRtaResponse 定义微博RTA响应的数据结构
type WeiboRtaResponse struct {
	Errno       string                    `json:"errno"`             // 错误码，0表示成功，非0表示发生错误
	Message     string                    `json:"message,omitempty"` // 错误信息
	ID          string                    `json:"id"`                // 对应请求的唯一ID
	Bidresponse []WeiboRtaBidresponseInfo `json:"bidresponse"`       // 竞价响应信息集合
}

// WeiboRtaBidresponseInfo 定义微博RTA竞价响应信息结构
type WeiboRtaBidresponseInfo struct {
	RtaID     string `json:"rta_id"`     // RTA ID，标识特定广告主
	IsBidding int    `json:"is_bidding"` // 是否投放：1-是，2-否
}

// IsWeiboRtaOK 实现微博RTA实时竞价接口
// 该函数发送设备信息到微博RTA服务，判断是否可以为该设备投放广告
// 文档：https://kea58ff4ba.docs.qq.com/doc/DQVN6akhVYVVjRnZDcnV5TEp2
// 参数:
//   - ctx: 上下文，用于控制请求超时等
//   - redisClient: Redis客户端，用于可能的缓存操作
//   - bigdataUID: 请求唯一标识
//   - deviceInfo: 设备信息
//   - maplehazeRTAID: 枫岚互联RTA ID
//
// 返回:
//   - bool: 是否可以投放广告
//   - error: 错误信息
func IsWeiboRtaOK(ctx context.Context, redisClient databases.Redis, reqId string, deviceInfo *models.MHDeviceStu, maplehazeRTAID string) (bool, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Println("IsWeiboRtaOK recover error:", err)
		}
	}()

	// 记录开始时间，用于计算请求耗时
	startTime := time.Now()

	// 参数验证，清理并修复设备标识符
	paramRepair(deviceInfo)

	// 检查RTA ID是否为空
	if len(maplehazeRTAID) == 0 {
		log.Println("weibo_rta maplehazeRTAID is empty error")
		return false, errors.New("weibo_rta, empty maplehazeRTAID")
	}

	// 构建请求参数，组装设备信息
	request, isDeviceOK := composeWeiboRta(deviceInfo, reqId, maplehazeRTAID)
	if !isDeviceOK {
		log.Printf("weibo_rta device is not ok reqId=%v, maplehazeRTAID=%v, deviceInfo=%v\n", reqId, maplehazeRTAID, deviceInfo)
		return false, errors.New("weibo_rta device is not ok")
	}

	// 发送HTTP请求到微博RTA服务
	bodyContent, retCode, err := GetHTTPClient().DoWithTimeout(
		ctx,
		weiboRtaTimeout,
		http.MethodPost,
		weiboRtaUrl,
		utilities.WithHeaders(map[string]string{"Content-Type": "application/json"}),
		utilities.WithJSONBody(request),
	)

	// 处理请求错误
	if err != nil {
		log.Printf("weibo_rta request failed, reqId=%v, maplehazeRTAID=%v, err=%v\n", reqId, maplehazeRTAID, err)
		return false, fmt.Errorf("weibo_rta request failed: %w", err)
	}

	// 处理非200状态码
	if retCode != http.StatusOK {
		log.Printf("weibo_rta respose error, reqId=%v, retCode=%v\n", reqId, retCode)
		return false, fmt.Errorf("weibo_rta wrong status code: %d", retCode)
	}

	// 解析JSON响应
	var response WeiboRtaResponse
	if err = jsoniter.Unmarshal(bodyContent, &response); err != nil {
		log.Printf("weibo_rta unmarshal response failed, reqId=%v, bodyContent=%v, err=%v\n", reqId, string(bodyContent), err)
		return false, err
	}

	// 检查响应状态
	if response.Errno != weiboRspCodeSuccess || len(response.Bidresponse) == 0 {
		log.Printf("weibo_rta response error, reqId=%v, bodyContent=%v\n", reqId, string(bodyContent))
		return false, fmt.Errorf("weibo_rta response error: %s", response.Message)
	}
	isBidding := response.Bidresponse[0].IsBidding

	// 记录响应信息和请求耗时
	log.Printf("weibo_rta response success reqId=%v, maplehazeRTAID=%v, isBidding=%v, elapsed=%v\n", reqId, maplehazeRTAID, isBidding, time.Since(startTime))
	return response.Bidresponse[0].IsBidding == weiboIsBidding, nil
}

// 组装微博请求参数
func composeWeiboRta(deviceInfo *models.MHDeviceStu, reqId, maplehazeRTAID string) (WeiboRtaRequest, bool) {
	// 获取枫岚互联RTA ID
	rtaId, ok := maplehazeRtaIdMap[maplehazeRTAID]
	if !ok {
		log.Printf("weibo_rta rtaId is not found, maplehazeRTAID=%v\n", maplehazeRTAID)
		return WeiboRtaRequest{}, false
	}

	// 验证设备信息，确保至少有一个有效的设备标识符
	isDeviceOK := false

	weiboDevice := WeiboRtaDeviceInfo{
		OS: deviceInfo.Os,
	}

	// 根据操作系统类型处理不同的设备标识符
	if deviceInfo.Os == "android" {
		// 处理Android设备信息：优先使用IMEI和OAID
		if len(deviceInfo.Imei) > 0 {
			weiboDevice.Imei = deviceInfo.Imei
			weiboDevice.ImeiMd5 = strings.ToLower(utils.GetMd5(deviceInfo.Imei))
			isDeviceOK = true
		} else if len(deviceInfo.ImeiMd5) > 0 && deviceInfo.ImeiMd5 != utils.GetMd5("") {
			weiboDevice.ImeiMd5 = strings.ToLower(deviceInfo.ImeiMd5)
			isDeviceOK = true
		}

		if len(deviceInfo.Oaid) > 0 {
			weiboDevice.Oaid = deviceInfo.Oaid
			weiboDevice.OaidMd5 = strings.ToLower(utils.GetMd5(deviceInfo.Oaid))
			isDeviceOK = true
		} else if len(deviceInfo.OaidMd5) > 0 && deviceInfo.OaidMd5 != utils.GetMd5("") {
			weiboDevice.OaidMd5 = strings.ToLower(deviceInfo.OaidMd5)
			isDeviceOK = true
		}
	} else if deviceInfo.Os == "ios" {
		// 处理iOS设备信息：使用IDFA和CAID
		if len(deviceInfo.Idfa) > 0 {
			weiboDevice.Idfa = deviceInfo.Idfa
			weiboDevice.IdfaMd5 = strings.ToLower(utils.GetMd5(deviceInfo.Idfa))
			isDeviceOK = true
		} else if len(deviceInfo.IdfaMd5) > 0 && deviceInfo.IdfaMd5 != utils.GetMd5("") {
			weiboDevice.IdfaMd5 = strings.ToLower(deviceInfo.IdfaMd5)
			isDeviceOK = true
		}

		// 添加CAID信息，用于iOS 14.5+设备
		if len(deviceInfo.CAIDMulti) > 0 {
			weiboDevice.CAIDMulti = make([]WeiboRtaCAIDMultiInfo, 0, len(deviceInfo.CAIDMulti))
			for _, caid := range deviceInfo.CAIDMulti {
				if len(caid.CAID) > 0 && len(caid.CAIDVersion) > 0 {
					weiboDevice.CAIDMulti = append(weiboDevice.CAIDMulti, WeiboRtaCAIDMultiInfo{
						CAID:        caid.CAID,
						CAIDVersion: caid.CAIDVersion,
					})
					isDeviceOK = true
				}
			}
		}
	} else {
		// 不支持除android和ios之外的操作系统
		return WeiboRtaRequest{}, false
	}

	// 构建完整的请求结构
	return WeiboRtaRequest{
		ReqID:   reqId,
		ReqTime: time.Now().UnixMilli(), // 使用毫秒级时间戳
		RtaID:   []string{rtaId},
		Device:  weiboDevice,
	}, isDeviceOK
}
