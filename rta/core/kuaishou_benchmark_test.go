package core

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/pb/kuaishou_rta"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	"google.golang.org/protobuf/proto"
)

// BenchmarkKuaishouHttpRequest_Success 基准测试快手RTA HTTP请求成功场景
func BenchmarkKuaishouHttpRequest_Success(b *testing.B) {
	// 创建模拟的成功响应
	successResponse := &kuaishou_rta.RtaResponse{
		StatusCode: 0,
		PromotionResult: []*kuaishou_rta.RtaResponse_PromotionResult{
			{
				PromotionType: "kwai_laxin",
				Accept:        true,
			},
		},
	}
	responseBytes, _ := proto.Marshal(successResponse)

	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write(responseBytes)
	}))
	defer server.Close()

	// 准备测试请求数据
	testRequest := &kuaishou_rta.RtaRequest{
		RequestId:   "test-request-id",
		Channel:     "test-channel",
		RequestTime: time.Now().UnixMilli(),
		Sign:        "test-sign",
		Device: &kuaishou_rta.RtaRequest_Device{
			Imei: "123456789012345",
		},
		PromotionType: []string{"kwai_laxin"},
	}
	requestBytes, _ := proto.Marshal(testRequest)

	// 重置计时器
	b.ResetTimer()

	// 运行基准测试
	for i := 0; i < b.N; i++ {
		ctx := context.Background()
		_, _, err := GetHTTPClient().DoWithTimeout(
			ctx,
			200*time.Millisecond,
			http.MethodPost,
			server.URL,
			utilities.WithHeaders(map[string]string{"Content-Type": "application/x-protobuf; charset=utf-8"}),
			utilities.WithProtobufBody(requestBytes),
		)
		if err != nil {
			b.Fatalf("HTTP request failed: %v", err)
		}
	}
}

// BenchmarkKuaishouHttpRequest_LargePayload 基准测试快手RTA HTTP请求大负载场景
func BenchmarkKuaishouHttpRequest_LargePayload(b *testing.B) {
	// 创建模拟的成功响应
	successResponse := &kuaishou_rta.RtaResponse{
		StatusCode: 0,
		PromotionResult: []*kuaishou_rta.RtaResponse_PromotionResult{
			{
				PromotionType: "kwai_laxin",
				Accept:        true,
			},
		},
	}
	responseBytes, _ := proto.Marshal(successResponse)

	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write(responseBytes)
	}))
	defer server.Close()

	// 创建包含大量推广类型的请求（模拟大数据）
	promotionTypes := make([]string, 100)
	for i := 0; i < 100; i++ {
		promotionTypes[i] = "kwai_laxin_type_" + string(rune(i))
	}

	testRequest := &kuaishou_rta.RtaRequest{
		RequestId:   "test-request-id",
		Channel:     "test-channel",
		RequestTime: time.Now().UnixMilli(),
		Sign:        "test-sign",
		Device: &kuaishou_rta.RtaRequest_Device{
			Imei:    "123456789012345",
			ImeiMd5: "5d41402abc4b2a76b9719d911017c592",
			Oaid:    "test-oaid-value",
			OaidMd5: "098f6bcd4621d373cade4e832627b4f6",
		},
		PromotionType: promotionTypes,
	}
	requestBytes, _ := proto.Marshal(testRequest)

	// 重置计时器
	b.ResetTimer()

	// 运行基准测试
	for i := 0; i < b.N; i++ {
		ctx := context.Background()
		_, _, err := GetHTTPClient().DoWithTimeout(
			ctx,
			200*time.Millisecond,
			http.MethodPost,
			server.URL,
			utilities.WithHeaders(map[string]string{"Content-Type": "application/x-protobuf; charset=utf-8"}),
			utilities.WithProtobufBody(requestBytes),
		)
		if err != nil {
			b.Fatalf("HTTP request failed: %v", err)
		}
	}
}

// BenchmarkKuaishouHttpRequest_Parallel 并行基准测试快手RTA HTTP请求
func BenchmarkKuaishouHttpRequest_Parallel(b *testing.B) {
	// 创建模拟的成功响应
	successResponse := &kuaishou_rta.RtaResponse{
		StatusCode: 0,
		PromotionResult: []*kuaishou_rta.RtaResponse_PromotionResult{
			{
				PromotionType: "kwai_laxin",
				Accept:        true,
			},
		},
	}
	responseBytes, _ := proto.Marshal(successResponse)

	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write(responseBytes)
	}))
	defer server.Close()

	// 准备测试请求数据
	testRequest := &kuaishou_rta.RtaRequest{
		RequestId:   "test-request-id",
		Channel:     "test-channel",
		RequestTime: time.Now().UnixMilli(),
		Sign:        "test-sign",
		Device: &kuaishou_rta.RtaRequest_Device{
			Imei: "123456789012345",
		},
		PromotionType: []string{"kwai_laxin"},
	}
	requestBytes, _ := proto.Marshal(testRequest)

	// 重置计时器
	b.ResetTimer()

	// 运行并行基准测试
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			ctx := context.Background()
			_, _, err := GetHTTPClient().DoWithTimeout(
				ctx,
				200*time.Millisecond,
				http.MethodPost,
				server.URL,
				utilities.WithHeaders(map[string]string{"Content-Type": "application/x-protobuf; charset=utf-8"}),
				utilities.WithProtobufBody(requestBytes),
			)
			if err != nil {
				b.Fatalf("HTTP request failed: %v", err)
			}
		}
	})
}

// BenchmarkProtobufMarshal 基准测试protobuf序列化性能
func BenchmarkProtobufMarshal(b *testing.B) {
	testRequest := &kuaishou_rta.RtaRequest{
		RequestId:   "test-request-id",
		Channel:     "test-channel",
		RequestTime: time.Now().UnixMilli(),
		Sign:        "test-sign",
		Device: &kuaishou_rta.RtaRequest_Device{
			Imei:    "123456789012345",
			ImeiMd5: "5d41402abc4b2a76b9719d911017c592",
			Oaid:    "test-oaid-value",
			OaidMd5: "098f6bcd4621d373cade4e832627b4f6",
		},
		PromotionType: []string{"kwai_laxin", "kwai_lite_laxin", "kwai_lahui"},
	}

	// 重置计时器
	b.ResetTimer()

	// 运行基准测试
	for i := 0; i < b.N; i++ {
		_, err := proto.Marshal(testRequest)
		if err != nil {
			b.Fatalf("Protobuf marshal failed: %v", err)
		}
	}
}

// BenchmarkProtobufUnmarshal 基准测试protobuf反序列化性能
func BenchmarkProtobufUnmarshal(b *testing.B) {
	// 准备测试响应数据
	testResponse := &kuaishou_rta.RtaResponse{
		StatusCode: 0,
		PromotionResult: []*kuaishou_rta.RtaResponse_PromotionResult{
			{
				PromotionType: "kwai_laxin",
				Accept:        true,
			},
			{
				PromotionType: "kwai_lite_laxin",
				Accept:        false,
			},
			{
				PromotionType: "kwai_lahui",
				Accept:        true,
			},
		},
	}
	responseBytes, _ := proto.Marshal(testResponse)

	// 重置计时器
	b.ResetTimer()

	// 运行基准测试
	for i := 0; i < b.N; i++ {
		var response kuaishou_rta.RtaResponse
		err := proto.Unmarshal(responseBytes, &response)
		if err != nil {
			b.Fatalf("Protobuf unmarshal failed: %v", err)
		}
	}
}
