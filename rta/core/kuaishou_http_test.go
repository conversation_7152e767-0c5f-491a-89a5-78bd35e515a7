package core

import (
	"context"
	"errors"
	"net/http"
	"strings"
	"testing"
	"time"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/pb/kuaishou_rta"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/utils"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
	"github.com/gofrs/uuid"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
)

const (
	kuaishouUrl     = "https://promotion-partner.kuaishou.com/rest/n/rta/common/kfy"
	kuaishouToken   = "w2y17"
	kuaishouChannel = "GERUI"
)

// TestKuaishouHttpRequest_LargeProtobufBody 测试快手RTA HTTP请求大protobuf数据场景
func TestKuaishouHttpRequest(t *testing.T) {

	kuaishouReqDevice := &kuaishou_rta.RtaRequest_Device{}
	isDeviceOK := false

	// device
	if deviceInfo.Os == "android" {
		if len(deviceInfo.Imei) > 0 {
			kuaishouReqDevice.Imei = deviceInfo.Imei

			isDeviceOK = true
		} else if len(deviceInfo.ImeiMd5) > 0 && deviceInfo.ImeiMd5 != utils.GetMd5("") {
			kuaishouReqDevice.ImeiMd5 = strings.ToLower(deviceInfo.ImeiMd5)

			isDeviceOK = true
		}
		if len(deviceInfo.Oaid) > 0 {
			kuaishouReqDevice.Oaid = deviceInfo.Oaid

			isDeviceOK = true
		}
		if len(deviceInfo.OaidMd5) > 0 {
			kuaishouReqDevice.OaidMd5 = deviceInfo.OaidMd5

			isDeviceOK = true
		}
	} else if deviceInfo.Os == "ios" {
		if len(deviceInfo.Idfa) > 0 {
			kuaishouReqDevice.Idfa = deviceInfo.Idfa

			isDeviceOK = true
		} else if len(deviceInfo.IdfaMd5) > 0 && deviceInfo.IdfaMd5 != utils.GetMd5("") {
			kuaishouReqDevice.IdfaMd5 = strings.ToLower(deviceInfo.IdfaMd5)

			isDeviceOK = true
		}
	} else {
		return false, errors.New("ks_rta, wrong os")
	}

	currentTime := utils.GetCurrentMilliSecond()
	bigdataUID := uuid.NewV4().String()
	testRequest := &kuaishou_rta.RtaRequest{
		RequestId:   bigdataUID,
		Channel:     kuaishouChannel,
		RequestTime: currentTime,
		Sign:        utils.GetMd5(bigdataUID + utils.ConvertInt64ToString(currentTime) + kuaishouToken),
		Device: &kuaishou_rta.RtaRequest_Device{
			Imei: "123456789012345",
		},
		PromotionType: []string{"kwai_laxin"},
	}
	requestBytes, err := proto.Marshal(testRequest)
	assert.NoError(t, err)

	// 执行HTTP请求
	ctx := context.Background()
	bodyContent, retCode, err := GetHTTPClient().DoWithTimeout(
		ctx,
		200*time.Millisecond,
		http.MethodPost,
		kuaishouUrl,
		utilities.WithHeaders(map[string]string{"Content-Type": "application/x-protobuf; charset=utf-8"}),
		utilities.WithProtobufBody(requestBytes),
	)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 200, retCode)
	assert.NotEmpty(t, bodyContent)

	// 验证响应内容
	var response kuaishou_rta.RtaResponse
	err = proto.Unmarshal(bodyContent, &response)
	assert.NoError(t, err)
	assert.Equal(t, int32(0), response.StatusCode)
}
