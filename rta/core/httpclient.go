package core

import (
	"sync"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/utilities"
)

// globalHTTPClient 全局HTTP客户端
var (
	httpClient     *utilities.HttpClient
	httpClientOnce sync.Once
)

// GetGlobalHTTPClient 获取全局HTTP客户端（单例模式）
func GetHTTPClient() *utilities.HttpClient {
	httpClientOnce.Do(func() {
		httpClient = utilities.NewClient(
		// utilities.WithDebugLog(true),
		)
	})
	return httpClient
}

func CloseHTTPClient() {
	httpClient.Close()
}

var (
	fastHttpClient     *utilities.FastHttpClient
	fastHttpClientOnce sync.Once
)

// GetGlobalHTTPClient 获取全局HTTP客户端（单例模式）
func GetFastHTTPClient() *utilities.FastHttpClient {
	fastHttpClientOnce.Do(func() {
		fastHttpClient = utilities.NewFastClient()
	})
	return fastHttpClient
}

func CloseFastHTTPClient() {
	fastHttpClient.Close()
}
