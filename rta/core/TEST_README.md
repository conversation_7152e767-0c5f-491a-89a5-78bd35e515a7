# 快手RTA HTTP请求单元测试

本目录包含了针对快手RTA HTTP请求功能的详细单元测试和基准测试。

## 测试文件说明

### kuaishou_http_test.go
包含了针对HTTP请求部分的详细单元测试，覆盖以下场景：

1. **成功请求场景** (`TestKuaishouHttpRequest_Success`)
   - 测试正常的HTTP 200响应
   - 验证请求方法、头部和响应内容
   - 验证protobuf数据的正确序列化和反序列化

2. **超时场景** (`TestKuaishouHttpRequest_Timeout`)
   - 测试200ms超时设置
   - 验证超时错误处理

3. **服务器错误场景** (`TestKuaishouHttpRequest_ServerError`)
   - 测试HTTP 500错误响应
   - 验证错误状态码处理

4. **无效protobuf响应场景** (`TestKuaishouHttpRequest_InvalidProtobuf`)
   - 测试服务器返回无效protobuf数据
   - 验证protobuf解析错误处理

5. **上下文取消场景** (`TestKuaishouHttpRequest_ContextCancellation`)
   - 测试上下文取消机制
   - 验证请求被正确取消

6. **空protobuf数据场景** (`TestKuaishouHttpRequest_EmptyProtobufBody`)
   - 测试空请求体处理
   - 验证服务器对空数据的响应

7. **大protobuf数据场景** (`TestKuaishouHttpRequest_LargeProtobufBody`)
   - 测试大数据量请求处理
   - 验证大负载下的性能

### kuaishou_benchmark_test.go
包含了性能基准测试，用于评估HTTP请求的性能：

1. **基础性能测试** (`BenchmarkKuaishouHttpRequest_Success`)
   - 测试标准HTTP请求的性能

2. **大负载性能测试** (`BenchmarkKuaishouHttpRequest_LargePayload`)
   - 测试大数据量请求的性能

3. **并行性能测试** (`BenchmarkKuaishouHttpRequest_Parallel`)
   - 测试并发请求的性能

4. **Protobuf序列化性能测试** (`BenchmarkProtobufMarshal`)
   - 测试protobuf序列化的性能

5. **Protobuf反序列化性能测试** (`BenchmarkProtobufUnmarshal`)
   - 测试protobuf反序列化的性能

## 运行测试

### 运行所有单元测试
```bash
cd rta/core
go test -v
```

### 运行特定测试
```bash
# 运行HTTP相关测试
go test -v -run TestKuaishouHttpRequest

# 运行成功场景测试
go test -v -run TestKuaishouHttpRequest_Success

# 运行超时测试
go test -v -run TestKuaishouHttpRequest_Timeout
```

### 运行基准测试
```bash
# 运行所有基准测试
go test -bench=.

# 运行特定基准测试
go test -bench=BenchmarkKuaishouHttpRequest_Success

# 运行基准测试并显示内存分配
go test -bench=. -benchmem

# 运行基准测试指定次数
go test -bench=. -count=5
```

### 生成测试覆盖率报告
```bash
# 生成覆盖率报告
go test -coverprofile=coverage.out

# 查看覆盖率
go tool cover -func=coverage.out

# 生成HTML覆盖率报告
go tool cover -html=coverage.out -o coverage.html
```

## 测试重点

### HTTP请求验证
- ✅ 请求方法验证 (POST)
- ✅ Content-Type头部验证 (`application/x-protobuf; charset=utf-8`)
- ✅ 超时设置验证 (200ms)
- ✅ 请求体protobuf数据验证
- ✅ 响应状态码验证
- ✅ 响应体protobuf数据验证

### 错误处理验证
- ✅ 网络超时错误
- ✅ HTTP错误状态码
- ✅ 上下文取消
- ✅ 无效protobuf数据
- ✅ 空数据处理

### 性能验证
- ✅ 基础请求性能
- ✅ 大数据量处理性能
- ✅ 并发请求性能
- ✅ Protobuf序列化/反序列化性能

## 测试数据

### 标准测试请求
```go
testRequest := &kuaishou_rta.RtaRequest{
    RequestId:   "test-request-id",
    Channel:     "test-channel", 
    RequestTime: time.Now().UnixMilli(),
    Sign:        "test-sign",
    Device: &kuaishou_rta.RtaRequest_Device{
        Imei: "123456789012345",
    },
    PromotionType: []string{"kwai_laxin"},
}
```

### 标准测试响应
```go
successResponse := &kuaishou_rta.RtaResponse{
    StatusCode: 0,
    PromotionResult: []*kuaishou_rta.RtaResponse_PromotionResult{
        {
            PromotionType: "kwai_laxin",
            Accept:        true,
        },
    },
}
```

## 注意事项

1. **测试隔离**: 每个测试都使用独立的httptest.Server，确保测试之间不会相互影响
2. **超时设置**: 所有测试都使用200ms的超时设置，与生产环境保持一致
3. **错误验证**: 测试不仅验证成功场景，还重点验证各种错误场景的处理
4. **性能基准**: 基准测试提供了性能基线，可用于性能回归检测
5. **真实模拟**: 使用真实的protobuf数据和HTTP服务器模拟，确保测试的真实性

## 扩展测试

如需添加更多测试场景，可以参考现有测试的模式：

1. 创建httptest.Server模拟服务器
2. 准备测试数据（请求和响应）
3. 执行HTTP请求
4. 验证结果和错误处理

这些测试为快手RTA HTTP请求功能提供了全面的质量保障。
