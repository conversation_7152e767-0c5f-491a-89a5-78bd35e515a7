package core

import (
	"context"
	"errors"
	"log"

	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/models"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-ADX/adx_common/rta/utils"
	"codeup.aliyun.com/5f197749db0493ecef90a948/Maplehaze-Base/golang_common/databases"
)

// IsMaplehazeRtaOK ...
func IsMaplehazeRtaOK(ctx context.Context, redisClient databases.Redis, maplehazeRedisKey string, deviceInfo *models.MHDeviceStu) (bool, error) {
	if redisClient == nil {
		log.Println("redis is nil")
		return false, errors.New("redis is nil")
	}

	if len(maplehazeRedisKey) == 0 {
		log.Println("wrong config key")
		return false, errors.New("wrong config key")
	}

	if deviceInfo.DIDMd5 == utils.Get16Md5("") {
		log.Println("empty did_md5")
		return false, errors.New("mpty did_md5")
	}

	redisKey := "dmp_crowd_" + utils.Get16Md5(maplehazeRedisKey) + "_" + deviceInfo.DIDMd5
	_, redisErr := redisClient.Get(ctx, utils.Timeout50mill, redisKey)
	if redisErr != nil {
		log.Printf("no in crowd lib:%v\n", redisKey)
		return false, redisErr
	}

	return true, nil
}
