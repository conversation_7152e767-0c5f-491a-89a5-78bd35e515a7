package utils

import (
	"crypto/md5"
	"crypto/sha1"
	"encoding/hex"
	"log"
	"net"
	"strconv"
	"strings"
	"time"
)

// GetMd5 ...
func GetMd5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// Get16Md5 ...
func Get16Md5(str string) string {
	return GetMd5(str)[8:24]
}

// GetSha1 ...
func GetSha1(str string) string {
	sha1 := sha1.New()
	sha1.Write([]byte(str))
	return hex.EncodeToString(sha1.Sum([]byte("")))

}

// ConvertStringToInt ...
func ConvertStringToInt(str string) int {
	value, _ := strconv.Atoi(str)
	return value
}

// ConvertStringToInt64 ...
func ConvertStringToInt64(str string) int64 {
	value, _ := strconv.ParseInt(str, 10, 64)
	return value
}

// ConvertIntToString ...
func ConvertIntToString(value int) string {
	return strconv.Itoa(value)
}

// ConvertInt64ToString ...
func ConvertInt64ToString(value int64) string {
	return strconv.FormatInt(value, 10)
}

// ConvertStringToFloat ...
func ConvertStringToFloat(str string) float64 {
	value, _ := strconv.ParseFloat(str, 32)
	return value
}

// ConvertFloat32ToString 保留小数点后2位
func ConvertFloat32ToString(value float32) string {
	return strconv.FormatFloat(float64(value), 'f', 2, 32)
}

func IsNum(s string) bool {
	_, err := strconv.ParseFloat(s, 64)
	return err == nil
}

func checkIP(ipStr string) bool {
	address := net.ParseIP(ipStr)
	if address == nil {
		log.Printf("wrong ip: %v\n", address.String())
		return false
	}
	// log.Println("right ip: ", address.String())
	return true
}

// ip to int64
func inetAton(ipStr string) int64 {
	bits := strings.Split(ipStr, ".")

	b0, _ := strconv.Atoi(bits[0])
	b1, _ := strconv.Atoi(bits[1])
	b2, _ := strconv.Atoi(bits[2])
	b3, _ := strconv.Atoi(bits[3])

	var sum int64

	sum += int64(b0) << 24
	sum += int64(b1) << 16
	sum += int64(b2) << 8
	sum += int64(b3)

	return sum
}

// IsInnerIP ...
func IsInnerIP(ipStr string) bool {
	if !checkIP(ipStr) {
		return false
	}
	inputIPNum := inetAton(ipStr)
	innerIPA := inetAton("**************")
	innerIPB := inetAton("**************")
	innerIPC := inetAton("***************")
	innerIPD := inetAton("**************")
	innerIPF := inetAton("***************")

	return inputIPNum>>24 == innerIPA>>24 || inputIPNum>>20 == innerIPB>>20 ||
		inputIPNum>>16 == innerIPC>>16 || inputIPNum>>22 == innerIPD>>22 ||
		inputIPNum>>24 == innerIPF>>24
}

// IsImei ...
func IsImei(imei string) bool {
	// meid
	if len(imei) == 14 {
		return true
	}
	// imei
	if len(imei) != 15 {
		return false
	}

	prefix := imei[0 : len(imei)-1]
	// log.Println(prefix)
	lastDig := imei[len(imei)-1:]
	// log.Println(lastDig)

	var total, sum1, sum2 int
	n := len(prefix)
	for i := 0; i < n; i++ {
		num, _ := strconv.Atoi(string(prefix[i]))
		// 奇数
		if i%2 == 0 {
			sum1 += num
		} else { // 偶数
			tmp := num * 2
			if tmp < 10 {
				sum2 += tmp
			} else {
				sum2 = sum2 + tmp + 1 - 10
			}
		}
	}
	total = sum1 + sum2
	if total%10 == 0 {
		if ConvertStringToInt(lastDig) == 0 {
			return true
		}
	} else {
		if ConvertStringToInt(lastDig) == 10-(total%10) {
			return true
		}
	}
	return false
}

func GetCurrentNanoSecond() int64 {
	return time.Now().UnixNano()
}

// GetCurrentMilliSecond ...
func GetCurrentMilliSecond() int64 {
	return time.Now().UnixNano() / 1e6
}

// GetCurrentSecond ...
func GetCurrentSecond() int64 {
	return time.Now().Unix()
}

// GetCurrentHour ...
func GetCurrentHour() int {
	return time.Now().Hour()
}
